package com.misyn.mcms.claim.dao;

import com.misyn.mcms.claim.dto.AssessorAllocationDto;
import com.misyn.mcms.claim.dto.NotificationDto;
import com.misyn.mcms.claim.dto.UserDto;

import java.sql.Connection;
import java.util.List;

/**
 * Created by akila on 4/19/18.
 */
public interface AssessorAllocationDao extends BaseDao<AssessorAllocationDto> {
    String SQL_INSERT_TO_ASSESSORALLOCATION = "insert into claim_assign_assesor values(0,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";

    String SQL_SELECT_ALL_BY_CLAIM_ID = "SELECT\n"
            + "	t1.job_id AS \"t1.job_id\",\n"
            + "	t1.ref_no AS \"t1.ref_no\",\n"
            + "	t1.claim_no AS \"t1.claim_no\",\n"
            + "	t1.insepction_id AS \"t1.insepction_id\",\n"
            + "	t2.inspection_type_desc AS \"t2.inspection_type_desc\",\n"
            + "	CASE \n"
            + "		WHEN t1.insepction_id IN (8, 12) THEN t1.rte_code \n"
            + "		ELSE t3.V_NAME \n"
            + "	END AS \"t3.V_NAME\",\n"
            + "	t1.place_of_inspection AS \"t1.place_of_inspection\",\n"
            + "	t1.duration AS \"t1.duration\",\n"
            + "	t1.sms_status AS \"t1.sms_status\",\n"
            + "	t1.job_finished_datetime AS \"t1.job_finished_datetime\",\n"
            + "	t4.v_status_desc AS \"t4.v_status_desc\",\n"
            + "	t1.job_status AS \"t1.job_status\",\n"
            + "	t1.assign_datetime AS \"t1.assign_datetime\",\n"
            + "	t1.inp_userid AS \"t1.inp_userid\",\n"
            + "	t1.rte_code AS \"t1.rte_code\",\n"
            + "	t1.record_status AS \"t1.record_status\",\n"
            + "	t1.assessor_type AS \"t1.assessor_type\",\n"
            + "	t5.V_REASON AS \"t5.V_REASON\",\n"
            + "	t6.V_REASON AS \"t6.V_REASON\",\n"
            + "	t7.v_usrid AS \"t7.v_usrid\",\n"
            + "	t8.N_ID AS \"t8.N_ID\",\n"
            + "	t8.V_REASON AS reassign_reason,\n"
            + "	t1.is_commence_assessment AS \"t1.is_commence_assessment\",\n"
            + "	t1.commence_assessment_datetime AS \"t1.commence_assessment_datetime\",\n"
            + "	t11.V_OFFER_TYPE_DESC AS \"t11.V_OFFER_TYPE_DESC\", \n"
            + " t1.is_onsite_review AS \"t1.is_onsite_review\" \n"
            + "FROM\n"
            + "	claim_assign_assesor AS t1\n"
            + "	LEFT JOIN claim_assessor AS t3 ON t3.V_CODE = t1.assessor_code\n"
            + "	LEFT JOIN claim_inspection_type AS t2 ON t1.insepction_id = t2.inspection_type_id\n"
            + "	LEFT JOIN claim_status_para AS t4 ON t1.job_status = t4.n_ref_id\n"
            + "	LEFT JOIN claim_assessor_reject_reason AS t5 ON t5.N_ID = t1.reject_reason\n"
            + "	LEFT JOIN claim_assessor_reassign_reason AS t6 ON t6.N_ID = t1.reassigning_reason\n"
            + "	LEFT JOIN usr_mst AS t7 ON t1.assessor_code = t7.v_emp_no\n"
            + "	LEFT JOIN desktop_reassign_reason AS t8 ON t1.insepction_id = 8 \n"
            + "	AND t8.N_ID = t1.reassigning_reason\n"
            + "	LEFT JOIN onsite_inspection_details AS t9 ON t9.n_ref_no = t1.ref_no\n"
            + "	LEFT JOIN desktop_inspection_details AS t10 ON t10.n_ref_no = t1.ref_no\n"
            + "	LEFT JOIN onsite_inspection_details_me AS t12 ON t12.n_ref_no = t1.ref_no\n"
            + "	LEFT JOIN desktop_inspection_details_me AS t13 ON t13.n_ref_no = t1.ref_no\n"
            + "	LEFT JOIN claim_offer_type AS t11 ON (\n"
            + "		t11.N_OFFER_TYPE_ID = t9.offer_type \n"
            + "		OR t11.N_OFFER_TYPE_ID = t10.offer_type \n"
            + "		OR t11.N_OFFER_TYPE_ID = t12.offer_type \n"
            + "		OR t11.N_OFFER_TYPE_ID = t13.offer_type \n"
            + "	) \n"
            + "	AND t11.N_OFFER_TYPE_ID NOT IN ( 0 ) \n"
            + "WHERE\n"
            + "	t1.claim_no = ? \n"
            + "ORDER BY\n"
            + "	t1.ref_no DESC";

    String SQL_SELECT_ALL_FROM_JOB_ID = "SELECT\n" +
            "t1.*,t2.*,t3.*,t4.*\n" +
            "FROM\n" +
            "claim_assign_assesor AS t1\n" +
            "LEFT JOIN claim_assessor AS t3 ON t3.V_CODE = t1.assessor_code\n" +
            "LEFT JOIN claim_inspection_type AS t2 ON t1.insepction_id = t2.inspection_type_id\n" +
            "LEFT JOIN claim_status_para AS t4 ON t1.job_status = t4.n_ref_id\n" +
            "where t1.job_id=?\n";

    String SQL_SELECT_ALL_FROM_REF_NO = "SELECT\n" +
            "	t1.*,\n" +
            "	t2.*,\n" +
            "	t3.*,\n" +
            "	t4.*,\n" +
            "	t5.* \n" +
            "FROM\n" +
            "	claim_assign_assesor AS t1\n" +
            "	LEFT JOIN claim_assessor AS t3 ON t3.V_CODE = t1.assessor_code\n" +
            "	LEFT JOIN claim_inspection_type AS t2 ON t1.insepction_id = t2.inspection_type_id\n" +
            "	LEFT JOIN claim_status_para AS t4 ON t1.job_status = t4.n_ref_id\n" +
            "	LEFT JOIN usr_mst AS t5 ON t5.v_emp_no = t1.assessor_code \n" +
            "WHERE\n" +
            "	t1.ref_no =?";
    String SQL_UPDATE_CLAIM_ASSIGING_BY_PREVIOUS_JOB_ID = "update claim_assign_assesor set job_status=?,reassigning_reason=? where ref_no=?";

    String SQL_UPDATE_CLAIM_ASSIGING_COMPLTED_DATE_BY_PREVIOUS_JOB_ID = "update claim_assign_assesor set job_status=?,job_finished_datetime=? where ref_no=?";

    String SQL_UPDATE_CLAIM_ASSIGING_BY_JOB_ID = "update claim_assign_assesor set record_status=? where ref_no=?";

    String SQL_UPDATE_RECORD_STATUS_BY_JOB_ID = "update claim_assign_assesor set record_status=? where job_id=?";

    String SQL_UPDATE_CLAIM_ASSIGING_JOB_STATUS_BY_JOB_ID = "update claim_assign_assesor set job_status=? where job_id=?";

    String SQL_UPDATE_CLAIM_ASSIGING_RECORD_STATUS_BY_REF_NO = "update claim_assign_assesor set record_status=? where ref_no=?";

    String SQL_SELECT_RTE_BY_USER_MST = "SELECT  t1.v_usrid,t1.n_usrcode FROM usr_mst  as t1   WHERE t1.n_accessusrtype IN (?) AND v_usrstatus <> 'C' ORDER  BY t1.v_usrid asc";
    String SQL_SELECT_TC_BY_USER_MST = "SELECT  t1.v_usrid,t1.n_usrcode FROM usr_mst  as t1   WHERE t1.n_accessusrtype IN (?)";

    String SQL_SELECT_PENDING_JOBS_FOR_FOLLOW_UP_CALLS = "SELECT\n" +
            "\tt1.claim_no,\n" +
            "\tt1.job_id,\n" +
            "\tt2.N_POL_REF_NO,\n" +
            "\tt2.V_VEHICLE_NO,\n" +
            "\tt2.V_INPUSER,\n" +
            "\tt2.V_COVER_NOTE_NO,\n" +
            "\tt2.D_ACCID_DATE,\n" +
            "\tDATE_ADD( t1.assign_datetime, INTERVAL ( t1.duration + 15 ) MINUTE ) AS notifyDateTime,\n" +
            "\tt3.V_PRODUCT,\n" +
            "\tt3.V_CATEGORY_DESC \n" +
            "FROM\n" +
            "\tclaim_assign_assesor AS t1\n" +
            "\tINNER JOIN claim_claim_info_main AS t2 ON t1.claim_no = t2.N_CLIM_NO\n" +
            "\tINNER JOIN claim_vehicle_info_main AS t3 ON t2.N_POL_REF_NO = t3.N_POL_REF_NO \n" +
            "WHERE\n" +
            "\tt1.insepction_id NOT IN ( 8, 11 ) \n" +
            "\tAND t1.insepction_id IN ( 1 ) \n" +
            "\tAND DATE_ADD( t1.assign_datetime, INTERVAL ( t1.duration + 15 ) MINUTE ) < ?";

    String SQL_SELECT_ASSESOR_ALLOCATION_DETAILS = "SELECT * FROM claim_assign_assesor WHERE claim_no=? AND insepction_id IN (?) LIMIT 1";
    String SQL_SELECT_INSEPCTION_ID_ASSESOR_ALLOCATION_DETAILS = "SELECT insepction_id FROM claim_assign_assesor WHERE claim_no=? AND insepction_id =? AND record_status NOT IN(4, 23) LIMIT 1";

    String GET_INPUT_USER_NAME_USINF_REF_NO = "SELECT inp_userid FROM claim_assign_assesor WHERE ref_no =? ";
    String GET_RTE_NAME_USINF_REF_NO = "SELECT rte_code FROM claim_assign_assesor WHERE ref_no =? ";

    String SELECT_ASSESSOR_CODE_BY_REF_NO = "SELECT assessor_code FROM claim_assign_assesor WHERE ref_no =? ";

    String SQL_SELECT_PERVIOUS_INSPECTION = "SELECT * FROM claim_assign_assesor as t1 WHERE t1.claim_no=? AND t1.insepction_id =? ORDER BY t1.ref_no DESC LIMIT 1";

    String SQL_SELECT_ALL_BY_CLAIM_ID_SUPER = "SELECT\n" +
            "t1.job_id,\n" +
            "t1.ref_no,\n" +
            "t1.claim_no,\n" +
            "t1.insepction_id,\n" +
            "t2.inspection_type_desc,\n" +
            "t3.V_NAME,\n" +
            "t1.place_of_inspection,\n" +
            "t1.duration,\n" +
            "t1.sms_status,\n" +
            "t4.v_status_desc,\n" +
            "t1.job_status,\n" +
            "t1.job_finished_datetime,\n" +
            "t1.assign_datetime,\n" +
            "t1.inp_userid,\n" +
            "t1.rte_code,\n" +
            "t5.V_REASON,\n" +
            "t6.V_REASON,\n" +
            "t3.V_CODE\n" +
            "FROM\n" +
            "claim_assign_assesor AS t1\n" +
            "LEFT JOIN claim_assessor AS t3 ON t3.V_CODE = t1.assessor_code\n" +
            "LEFT JOIN claim_inspection_type AS t2 ON t1.insepction_id = t2.inspection_type_id\n" +
            "LEFT JOIN claim_status_para AS t4 ON t1.job_status = t4.n_ref_id\n" +
            "LEFT JOIN claim_assessor_reject_reason AS t5 ON t5.N_ID = t1.reject_reason\n" +
            "LEFT JOIN claim_assessor_reassign_reason AS t6 ON t6.N_ID = t1.reassigning_reason\n" +
            "WHERE t1.claim_no=?  ORDER BY t1.ref_no  DESC \n";

    String SQL_GET_INSPECTION_TYPE_BY_REF_NO = "SELECT\n" +
            "t1.inspection_type_desc \n" +
            "FROM\n" +
            "claim_inspection_type AS t1\n" +
            "INNER JOIN claim_assign_assesor AS t2 \n" +
            "WHERE\n" +
            "t2.insepction_id = t1.inspection_type_id \n" +
            "AND t2.ref_no = ?";

    String SQL_SELECT_All_INP_USER = "SELECT DISTINCT\n" +
            "inp_userid \n" +
            "FROM \n" +
            "claim_assign_assesor";

    String SELECT_ASSESSOR_TYPE_BY_REF_NO = "SELECT assessor_type FROM claim_assign_assesor WHERE ref_no =? ";

    String SQL_REJECT_CLAIM_ASSIGING_BY_PREVIOUS_JOB_ID = "update claim_assign_assesor set job_status=?,reject_reason=? where ref_no=?";

    String SQL_UPDATE_JOB_FINISHED_DATE = "UPDATE claim_assign_assesor SET job_finished_datetime = ? WHERE ref_no = ?";

    String SQL_GET_REPORTING_RTE = "SELECT  v_usrid, n_usrcode FROM usr_mst WHERE n_accessusrtype IN (?) and n_auth_level = 1";

    String SQL_UPDATE_ONLINE_INSPECTION_STATUS_BY_REF_NO = "update claim_assign_assesor set is_online_inspection = ? where ref_no = ?";

    List<AssessorAllocationDto> getAssessorListByClaimNo(Connection connection, int claimId) throws Exception;

    boolean updateJobStatusByJobId(Connection connection, int status, int reasonId, int refId) throws Exception;

    boolean updateJobStatusByJobId(Connection connection, int status, String datetime, String userId, String inp_datetime, int jobId) throws Exception;

    boolean updateRecordStatusByJobId(Connection connection, int status, int refNo) throws Exception;

    boolean updateJobStatusByJobId(Connection connection, int status, String jobId) throws Exception;

    boolean updateRecordStatusByRefNo(Connection connection, int statusId, int refNo) throws Exception;

    List<UserDto> getRTEList(Connection connection, String accessUserTypes) throws Exception;

    List<String> getInputUserList(Connection connection) throws Exception;

    List<UserDto> getTCList(Connection connection, String accessUserTypes) throws Exception;

    List<NotificationDto> getNotifiList(Connection connection, String currentDateTime) throws Exception;

    boolean getInspectionType(Connection connection, Integer claimNo, Integer inspctionId) throws Exception;

    List<UserDto> getUserList(Connection connection, String accessUserTypes) throws Exception;

    String getInputUser(Connection connection, int refNo) throws Exception;

    String getRTE(Connection connection, int refNo) throws Exception;

    AssessorAllocationDto getAssessorAllocationByClaimNoAndInspectionId(Connection connection, Integer claimNo, Integer inspectionId) throws Exception;

    List<AssessorAllocationDto> getAssessorListSuperByClaimNo(Connection connection, int claimId) throws Exception;

    String getAssessorCodeByRefNo(Connection connection, Integer keyId) throws Exception;

    String getInspectionTypeDesc(Connection connection, Integer id);

    String getAssessorTypeByRefNo(Connection connection, String refNo);

    boolean rejectJobStatusByJobId(Connection connection, int claimStatus, int reasonId, Integer previousRefId) throws Exception;

    void updateJobFinishedDate(Connection connection, int refNo) throws Exception;

    List<UserDto> getReportingRteList(Connection connection) throws Exception;

    boolean updateOnlineInspectionStatus(Connection connection, String statusId, int refNo) throws Exception;
}
