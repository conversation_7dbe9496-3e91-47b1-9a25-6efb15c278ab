package com.misyn.mcms.claim.dao.impl;

import com.misyn.mcms.claim.dao.AbstractBaseDao;
import com.misyn.mcms.claim.dao.AssessorAllocationDao;
import com.misyn.mcms.claim.dto.*;
import com.misyn.mcms.claim.job.impl.CallCenterJobImpl;
import com.misyn.mcms.utility.AppConstant;
import com.misyn.mcms.utility.Utility;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

public class AssessorAllocationDaoImpl extends AbstractBaseDao<AssessorAllocationDaoImpl> implements AssessorAllocationDao {

    private CallCenterJobImpl callCenterJob;
    private static final Logger LOGGER = LoggerFactory.getLogger(AssessorAllocationDaoImpl.class);

    @Override
    public AssessorAllocationDto insertMaster(Connection connection, AssessorAllocationDto assessorAllocationDto) throws Exception {
        AssessorAllocationDto assessorAllocation = null;
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(SQL_INSERT_TO_ASSESSORALLOCATION, Statement.RETURN_GENERATED_KEYS);
            ps.setString(1, assessorAllocationDto.getJobId());
            ps.setInt(2, assessorAllocationDto.getClaimsDto().getClaimNo());
            ps.setInt(3, assessorAllocationDto.getInspectionDto().getInspectionId());
            ps.setInt(4, assessorAllocationDto.getInspectionReasonDto().getReasonId());
            ps.setString(5, assessorAllocationDto.getPlaceOfinspection());
            ps.setString(6, assessorAllocationDto.getDistrictDto().getDistrictCode());
            ps.setInt(7, assessorAllocationDto.getCityDto().getGramaCode());
            ps.setString(8, assessorAllocationDto.getAssessorDto().getCode());
            ps.setString(9, assessorAllocationDto.getAssessorDto().getAssessorContactNo());
            ps.setInt(10, assessorAllocationDto.getResponse());
            ps.setInt(11, assessorAllocationDto.getJobStatusId());
            ps.setInt(12, assessorAllocationDto.getRejectReasonDto().getReasonId());
            ps.setString(13, assessorAllocationDto.getCurrentLocation());
            ps.setString(14, assessorAllocationDto.getAssignDatetime());
            ps.setString(15, assessorAllocationDto.getCustExpectedDatetime());
            ps.setInt(16, assessorAllocationDto.getDuration());
            ps.setString(17, assessorAllocationDto.getActualDatetime());
            ps.setString(18, assessorAllocationDto.getSmsBody());
            ps.setString(19, assessorAllocationDto.getSmsBody());
            ps.setString(20, assessorAllocationDto.getJobFinishedDatetime());
            ps.setString(21, assessorAllocationDto.getJobCloseDateTime());
            ps.setInt(22, assessorAllocationDto.getReassigningReasonDto().getId());
            ps.setInt(23, assessorAllocationDto.getRecordStatus());
            ps.setString(24, assessorAllocationDto.getRteCode());
            ps.setString(25, assessorAllocationDto.getGarageContactNo());
            ps.setString(26, assessorAllocationDto.getCallCenterRemark());
            ps.setString(27, assessorAllocationDto.getInputUserId());
            ps.setString(28, assessorAllocationDto.getInputDatetime());
            ps.setString(29, assessorAllocationDto.getPriority());
            ps.setString(30, assessorAllocationDto.getAssessorType());
            ps.setString(31, assessorAllocationDto.getIsCommenceAssessment());
            ps.setString(32, assessorAllocationDto.getCommenceAssessmentDatetime());
            ps.setString(33, assessorAllocationDto.getIsOnsiteReview());
            ps.setString(34, assessorAllocationDto.getIsPartnerGarage());
            if (ps.executeUpdate() > 0) {
                ResultSet rsKeys = ps.getGeneratedKeys();
                if (rsKeys.next()) {
                    int autoGeneratedId = rsKeys.getInt(1);
                    assessorAllocationDto.setRefNo(autoGeneratedId);
                    return assessorAllocationDto;
                }
                rsKeys.close();


            }
            checkSLADuration(assessorAllocationDto.getAssignDatetime(), assessorAllocationDto.getDuration());
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);

        } finally {
            if (ps != null) {
                ps.close();
            }
        }
        return null;
    }

    public void checkSLADuration(String assignDatetime, int duration) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime assignedTime = LocalDateTime.parse(assignDatetime, formatter);
        LocalDateTime allocatedTime = assignedTime.plusMinutes(duration);

        if (LocalDateTime.now().isAfter(allocatedTime)) {
            callCenterJob.sendAssessorReminder();
        }
    }

    @Override
    public AssessorAllocationDto insertTemporary(Connection connection, AssessorAllocationDto assessorAllocationDto) throws Exception {
        return null;
    }

    @Override
    public AssessorAllocationDto insertHistory(Connection connection, AssessorAllocationDto assessorAllocationDto) throws Exception {
        return null;
    }

    @Override
    public boolean deleteMaster(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public boolean deleteTemporary(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public AssessorAllocationDto searchMaster(Connection connection, Object id) throws Exception {
        PreparedStatement ps = null;
        ResultSet rst = null;

        try {
            ps = connection.prepareStatement(SQL_SELECT_ALL_FROM_REF_NO);
            ps.setObject(1, id);
            rst = ps.executeQuery();

            if (rst.next()) {
                return getAssessorAllocation(rst);
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return null;
    }

    @Override
    public AssessorAllocationDto searchTemporary(Connection connection, Object id) throws Exception {
        return null;
    }

    @Override
    public List<AssessorAllocationDto> searchAll(Connection connection) throws Exception {
        return null;
    }

    @Override
    public AssessorAllocationDto updateMaster(Connection connection, AssessorAllocationDto assessorAllocationDto) throws Exception {
        return null;
    }

    @Override
    public AssessorAllocationDto updateTemporary(Connection connection, AssessorAllocationDto assessorAllocationDto) throws Exception {
        return null;
    }

    @Override
    public String getMessage(Connection connection, int messageId) {
        return null;
    }

    @Override
    public List<AssessorAllocationDto> getAssessorListByClaimNo(Connection connection, int claimId) throws Exception {
        PreparedStatement ps = null;
        ResultSet rst = null;
        List<AssessorAllocationDto> list = new ArrayList<>();
        try {
            ps = connection.prepareStatement(SQL_SELECT_ALL_BY_CLAIM_ID);
            ps.setInt(1, claimId);
            rst = ps.executeQuery();
            while (rst.next()) {
                AssessorAllocationDto assessorAllocationDto = new AssessorAllocationDto();
                assessorAllocationDto.setJobId(rst.getString("t1.job_id"));
                InspectionDto inspectionDto = new InspectionDto();
                inspectionDto.setInspectionValue(rst.getString("t2.inspection_type_desc"));
                inspectionDto.setInspectionId(rst.getInt("t1.insepction_id"));
                inspectionDto.setOfferType(null == rst.getString("t11.V_OFFER_TYPE_DESC") ? AppConstant.STRING_EMPTY : rst.getString("t11.V_OFFER_TYPE_DESC"));
                assessorAllocationDto.setInspectionDto(inspectionDto);
                AssessorDto assessorDto = new AssessorDto();
                assessorDto.setName(rst.getString("t3.V_NAME"));
                assessorDto.setUserName(rst.getString("t7.v_usrid"));
                assessorAllocationDto.setAssessorDto(assessorDto);
                assessorAllocationDto.setPlaceOfinspection(rst.getString("t1.place_of_inspection"));
                assessorAllocationDto.setDuration(rst.getInt("t1.duration"));
                assessorAllocationDto.setSmsStatus(rst.getString("t1.sms_status"));
                assessorAllocationDto.setAssessorJobStatus(rst.getString("t4.v_status_desc"));
                assessorAllocationDto.setJobFinishedDatetime(Utility.getDate(rst.getString("t1.job_finished_datetime"), AppConstant.DATE_TIME_FORMAT).equals(AppConstant.DEFAULT_DATE_TIME) ?
                        AppConstant.STRING_EMPTY : Utility.getDate(rst.getString("t1.job_finished_datetime"), AppConstant.DATE_TIME_FORMAT));
                assessorAllocationDto.setAssignDatetime(Utility.getDate(rst.getString("t1.assign_datetime"), AppConstant.DATE_TIME_FORMAT));
                assessorAllocationDto.setJobStatusId(rst.getInt("t1.job_status"));
                assessorAllocationDto.setInputUserId(rst.getString("t1.inp_userid"));
                assessorAllocationDto.setRteCode(rst.getString("t1.rte_code"));
                assessorAllocationDto.setRefNo(rst.getInt("t1.ref_no"));
                assessorAllocationDto.setRecordStatus(rst.getInt("t1.record_status"));
                assessorAllocationDto.setAssessorType(null == rst.getString("t1.assessor_type") ? AppConstant.STRING_EMPTY : rst.getString("t1.assessor_type"));
                assessorAllocationDto.setIsOnsiteReview(rst.getString("t1.is_onsite_review"));

                RejectReasonDto rejectReasonDto = new RejectReasonDto();
                rejectReasonDto.setReasonoDescription(rst.getString("t5.V_REASON"));
                assessorAllocationDto.setRejectReasonDto(rejectReasonDto);
                ClaimsDto claimsDto = new ClaimsDto();
                claimsDto.setClaimNo(rst.getInt("t1.claim_no"));
                assessorAllocationDto.setClaimsDto(claimsDto);
                ReassigningReasonDto reassigningReasonDto = new ReassigningReasonDto();
                if (inspectionDto.getInspectionId() == AppConstant.DESKTOP_INSPECTION) {
                    reassigningReasonDto.setReasonValue(rst.getString("reassign_reason"));
                } else {
                    reassigningReasonDto.setReasonValue(rst.getString("t6.V_REASON"));
                }
                assessorAllocationDto.setReassigningReasonDto(reassigningReasonDto);

                assessorAllocationDto.setIsCommenceAssessment(rst.getString("t1.is_commence_assessment"));
                if (null != assessorAllocationDto.getIsCommenceAssessment() && AppConstant.YES.equalsIgnoreCase(assessorAllocationDto.getIsCommenceAssessment())) {
                    assessorAllocationDto.setCommenceAssessmentDatetime(Utility.getDate(rst.getString("t1.commence_assessment_datetime"), AppConstant.DATE_TIME_FORMAT));
                }

                list.add(assessorAllocationDto);
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            ps.close();
            rst.close();
        }
        return list;
    }

    @Override
    public boolean updateJobStatusByJobId(Connection connection, int status, int reasonId, int refId) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(SQL_UPDATE_CLAIM_ASSIGING_BY_PREVIOUS_JOB_ID);
            ps.setInt(1, status);
            ps.setInt(2, reasonId);
            ps.setInt(3, refId);
            return ps.executeUpdate() > 0;

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw new Exception("System Error", e);
        } finally {
            ps.close();
        }
    }

    @Override
    public boolean updateJobStatusByJobId(Connection connection, int status, String datetime, String userId, String inp_datetime, int refId) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(SQL_UPDATE_CLAIM_ASSIGING_COMPLTED_DATE_BY_PREVIOUS_JOB_ID);
            ps.setInt(1, status);
            ps.setString(2, datetime);
            ps.setInt(3, refId);
            return ps.executeUpdate() > 0;

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw new Exception("System Error", e);
        } finally {
            if (ps != null) {
                ps.close();
            }
        }

    }

    @Override
    public boolean updateRecordStatusByJobId(Connection connection, int status, int refNo) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(SQL_UPDATE_CLAIM_ASSIGING_BY_JOB_ID);
            ps.setInt(1, status);
            ps.setInt(2, refNo);
            return ps.executeUpdate() > 0;

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            ps.close();
        }
    }

    private AssessorAllocationDto getAssessorAllocation(ResultSet rst) {
        AssessorAllocationDto assessorAllocationDto = new AssessorAllocationDto();
        try {
            assessorAllocationDto.setRefNo(rst.getInt("t1.ref_no"));
            assessorAllocationDto.setJobId(rst.getString("t1.job_id"));
            ClaimsDto claimsDto = new ClaimsDto();
            claimsDto.setClaimNo(rst.getInt("t1.claim_no"));
            assessorAllocationDto.setClaimsDto(claimsDto);

            InspectionDto inspectionDto = new InspectionDto();
            inspectionDto.setInspectionId(rst.getInt("t2.inspection_type_id"));
            assessorAllocationDto.setInspectionDto(inspectionDto);

//            InspectionReasonDto inspectionReasonDto=new InspectionReasonDto();
//            inspectionReasonDto.setReasonId(rst.getInt("t5.N_ID"));
//            assessorAllocationDto.setInspectionReasonDto(inspectionReasonDto);
            assessorAllocationDto.setPlaceOfinspection(rst.getString("t1.place_of_inspection"));

            DistrictDto districtDto = new DistrictDto();
            districtDto.setDistrictCode(rst.getString("t1.district_code"));
            assessorAllocationDto.setDistrictDto(districtDto);

            CityDto cityDto = new CityDto();
            cityDto.setGramaCode(rst.getInt("t1.nearest_town"));
            assessorAllocationDto.setCityDto(cityDto);

            AssessorDto assessorDto = new AssessorDto();
            assessorDto.setName(rst.getString("t3.V_NAME"));
            assessorDto.setUserName(rst.getString("t5.v_usrid"));
            assessorDto.setCode(rst.getString("t1.assessor_code"));
            assessorDto.setAssessorContactNo(rst.getString("t1.assessor_contact_no"));
            assessorAllocationDto.setAssessorDto(assessorDto);

            assessorAllocationDto.setResponse(rst.getInt("t1.response"));
            assessorAllocationDto.setJobStatusId(rst.getInt("t1.job_status"));

            RejectReasonDto rejectReasonDto = new RejectReasonDto();
            rejectReasonDto.setReasonId(rst.getInt("reject_reason"));

            assessorAllocationDto.setRejectReasonDto(rejectReasonDto);
            assessorAllocationDto.setCurrentLocation(rst.getString("current_location"));
            assessorAllocationDto.setAssignDatetime(Utility.getDate(rst.getString("assign_datetime"), AppConstant.DATE_TIME_FORMAT));
            assessorAllocationDto.setDuration(rst.getInt("duration"));
            assessorAllocationDto.setPreviousJobId(rst.getString("t1.job_id"));
            assessorAllocationDto.setInputUserId(rst.getString("inp_userid"));
            assessorAllocationDto.setJobFinishedDatetime(Utility.getDate(rst.getString("job_finished_datetime"), AppConstant.DATE_TIME_FORMAT));
            assessorAllocationDto.setRecordStatus(rst.getInt("t1.record_status"));
            assessorAllocationDto.setGarageContactNo(rst.getString("garage_contact_no"));
            assessorAllocationDto.setIsPartnerGarage(rst.getString("is_partner_garage"));
            assessorAllocationDto.setIsOnsiteReview(rst.getString("is_onsite_review"));

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return assessorAllocationDto;

    }

    @Override
    public boolean updateJobStatusByJobId(Connection connection, int statusId, String jobId) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(SQL_UPDATE_CLAIM_ASSIGING_JOB_STATUS_BY_JOB_ID);
            ps.setInt(1, statusId);
            ps.setString(2, jobId);
            return ps.executeUpdate() > 0;
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            if (null != ps) {
                ps.close();
            }
        }
    }

    @Override
    public boolean updateRecordStatusByRefNo(Connection connection, int statusId, int refNo) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(SQL_UPDATE_CLAIM_ASSIGING_RECORD_STATUS_BY_REF_NO);
            ps.setInt(1, statusId);
            ps.setInt(2, refNo);
            return ps.executeUpdate() > 0;
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            if (null != ps) {
                ps.close();
            }
        }

    }

    @Override
    public List<UserDto> getRTEList(Connection connection, String accessUserTypes) throws Exception {
        PreparedStatement ps = null;
        List<UserDto> list = new ArrayList<>();
        try {
            ResultSet rs = null;
            ps = connection.prepareStatement(SQL_SELECT_RTE_BY_USER_MST.replace("?", accessUserTypes));
            rs = ps.executeQuery();

            while (rs.next()) {
                UserDto user = new UserDto();
                user.setUserId(rs.getString("t1.v_usrid"));
                user.setUserCode(rs.getInt("t1.n_usrcode"));
                list.add(user);
            }
            rs.close();
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return list;
    }

    @Override
    public List<String> getInputUserList(Connection connection) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<String> list = new ArrayList<>();
        try {
            ps = connection.prepareStatement(SQL_SELECT_All_INP_USER);
            rs = ps.executeQuery();
            while (rs.next()) {
                String inp_userid = rs.getString("inp_userid");
                list.add(inp_userid);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return list;
    }

    @Override
    public List<UserDto> getTCList(Connection connection, String accessUserTypes) throws Exception {
        PreparedStatement ps = null;
        List<UserDto> list = new ArrayList<>();
        try {
            ResultSet rs = null;
            ps = connection.prepareStatement(SQL_SELECT_RTE_BY_USER_MST.replace("?", accessUserTypes));

            rs = ps.executeQuery();

            while (rs.next()) {
                UserDto user = new UserDto();
                user.setUserId(rs.getString("t1.v_usrid"));
                user.setUserCode(rs.getInt("t1.n_usrcode"));
                list.add(user);
            }
        } catch (Exception e) {

        } finally {
            ps.close();
        }
        return list;
    }

    @Override
    public List<NotificationDto> getNotifiList(Connection connection, String currentDateTime) throws Exception {
        PreparedStatement ps = null;
        List<NotificationDto> list = new ArrayList<>();
        try {
            ResultSet rs = null;
            ps = connection.prepareStatement(SQL_SELECT_PENDING_JOBS_FOR_FOLLOW_UP_CALLS);
            ps.setString(1, currentDateTime);

            rs = ps.executeQuery();

            while (rs.next()) {
                NotificationDto notificationDto = new NotificationDto();
                notificationDto.setClaimNo(rs.getInt("t1.claim_no"));
                notificationDto.setNotifyDateTime(rs.getString("notifyDateTime"));
                notificationDto.setAssignUserId(rs.getString("t2.V_INPUSER"));
                notificationDto.setVehicleNo(rs.getString("t2.V_VEHICLE_NO"));
                notificationDto.setCoverNoteNo(rs.getString("t2.V_COVER_NOTE_NO"));
                notificationDto.setAccidentDate(rs.getString("t2.D_ACCID_DATE"));
                notificationDto.setProductName(rs.getString("t3.V_PRODUCT"));
                notificationDto.setCategoryDesc(rs.getString("t3.V_CATEGORY_DESC"));
                //  notificationDto.setJobNo(rs.getInt("t1.job_id"));
                list.add(notificationDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());

        } finally {
            ps.close();
        }
        return list;
    }

    @Override
    public boolean getInspectionType(Connection connection, Integer claimNo, Integer inspctionId) throws Exception {
        PreparedStatement ps = null;
        try {
            ResultSet rs = null;
            ps = connection.prepareStatement(SQL_SELECT_INSEPCTION_ID_ASSESOR_ALLOCATION_DETAILS);
            ps.setInt(1, claimNo);
            ps.setInt(2, inspctionId);

            rs = ps.executeQuery();

            if (rs.next()) {
                int insepction = rs.getInt("insepction_id");
                if (insepction != 0) {
                    return true;
                }
                //  AssessorAllocationDto assessorAllocation = getAssessorAllocation(rs);

            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());

        } finally {
            ps.close();
        }
        return false;
    }

    @Override
    public List<UserDto> getUserList(Connection connection, String accessUserTypes) throws Exception {
        PreparedStatement ps = null;
        List<UserDto> list = new ArrayList<>();
        try {
            ResultSet rs = null;
            ps = connection.prepareStatement(SQL_SELECT_RTE_BY_USER_MST.replace("?", accessUserTypes));

            rs = ps.executeQuery();

            while (rs.next()) {
                UserDto user = new UserDto();
                user.setUserId(rs.getString("t1.v_usrid"));
                user.setUserCode(rs.getInt("t1.n_usrcode"));
                list.add(user);
            }
        } catch (Exception e) {

        } finally {
            ps.close();
        }
        return list;
    }

    @Override
    public String getInputUser(Connection connection, int refNo) throws Exception {
        PreparedStatement ps = null;
        String Name = "";
        try {
            ResultSet rs = null;
            ps = connection.prepareStatement(GET_INPUT_USER_NAME_USINF_REF_NO);
            ps.setInt(1, refNo);
            rs = ps.executeQuery();
            while (rs.next()) {
                Name = rs.getString("inp_userid");
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            if (ps != null) {
                try {
                    ps.close();
                } catch (SQLException e) {
                    LOGGER.error(e.getMessage());
                }
            }
        }
        return Name;
    }

    @Override
    public String getRTE(Connection connection, int refNo) throws Exception {
        PreparedStatement ps = null;
        String Name = "";
        try {
            ResultSet rs = null;
            ps = connection.prepareStatement(GET_RTE_NAME_USINF_REF_NO);
            ps.setInt(1, refNo);
            rs = ps.executeQuery();
            while (rs.next()) {
                Name = rs.getString("rte_code");
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            if (ps != null) {
                try {
                    ps.close();
                } catch (SQLException e) {
                    LOGGER.error(e.getMessage());
                }
            }
        }
        return Name;
    }

    @Override
    public AssessorAllocationDto getAssessorAllocationByClaimNoAndInspectionId(Connection connection, Integer claimNo, Integer inspectionId) throws Exception {
        PreparedStatement ps = null;
        ResultSet rst = null;

        try {
            ps = connection.prepareStatement(SQL_SELECT_PERVIOUS_INSPECTION);
            ps.setObject(1, claimNo);
            ps.setObject(2, inspectionId);
            rst = ps.executeQuery();

            if (rst.next()) {
                AssessorAllocationDto assessorAllocationDto = new AssessorAllocationDto();
                assessorAllocationDto.setRefNo(rst.getInt("t1.ref_no"));
                return assessorAllocationDto;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return null;
    }

    @Override
    public List<AssessorAllocationDto> getAssessorListSuperByClaimNo(Connection connection, int claimId) throws Exception {
        PreparedStatement ps = null;
        ResultSet rst = null;
        List<AssessorAllocationDto> list = new ArrayList<>();
        try {
            ps = connection.prepareStatement(SQL_SELECT_ALL_BY_CLAIM_ID_SUPER);
            ps.setInt(1, claimId);
            rst = ps.executeQuery();
            while (rst.next()) {
                AssessorAllocationDto assessorAllocationDto = new AssessorAllocationDto();
                assessorAllocationDto.setJobId(rst.getString("t1.job_id"));
                InspectionDto inspectionDto = new InspectionDto();
                inspectionDto.setInspectionValue(rst.getString("t2.inspection_type_desc"));
                inspectionDto.setInspectionId(rst.getInt("t1.insepction_id"));
                assessorAllocationDto.setInspectionDto(inspectionDto);
                AssessorDto assessorDto = new AssessorDto();
                assessorDto.setName(rst.getString("t3.V_NAME"));
                assessorDto.setCode(rst.getString("t3.V_CODE"));
                assessorAllocationDto.setAssessorDto(assessorDto);
                assessorAllocationDto.setPlaceOfinspection(rst.getString("t1.place_of_inspection"));
                assessorAllocationDto.setDuration(rst.getInt("t1.duration"));
                assessorAllocationDto.setSmsStatus(rst.getString("t1.sms_status"));
                assessorAllocationDto.setAssessorJobStatus(rst.getString("t4.v_status_desc"));
                assessorAllocationDto.setJobFinishedDatetime(Utility.getDate(rst.getString("t1.job_finished_datetime"), AppConstant.DATE_TIME_FORMAT));
                assessorAllocationDto.setAssignDatetime(Utility.getDate(rst.getString("t1.assign_datetime"), AppConstant.DATE_TIME_FORMAT));
                assessorAllocationDto.setJobStatusId(rst.getInt("t1.job_status"));
                assessorAllocationDto.setInputUserId(rst.getString("t1.inp_userid"));
                assessorAllocationDto.setRteCode(rst.getString("t1.rte_code"));
                assessorAllocationDto.setRefNo(rst.getInt("t1.ref_no"));

                RejectReasonDto rejectReasonDto = new RejectReasonDto();
                rejectReasonDto.setReasonoDescription(rst.getString("t5.V_REASON"));
                assessorAllocationDto.setRejectReasonDto(rejectReasonDto);
                ClaimsDto claimsDto = new ClaimsDto();
                claimsDto.setClaimNo(rst.getInt("t1.claim_no"));
                assessorAllocationDto.setClaimsDto(claimsDto);
                ReassigningReasonDto reassigningReasonDto = new ReassigningReasonDto();
                reassigningReasonDto.setReasonValue(rst.getString("t6.V_REASON"));
                assessorAllocationDto.setReassigningReasonDto(reassigningReasonDto);

                list.add(assessorAllocationDto);
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            ps.close();
            rst.close();
        }
        return list;
    }

    @Override
    public String getAssessorCodeByRefNo(Connection connection, Integer keyId) throws Exception {
        PreparedStatement ps = null;
        try {
            ResultSet rs = null;
            ps = connection.prepareStatement(SELECT_ASSESSOR_CODE_BY_REF_NO);
            ps.setInt(1, keyId);
            rs = ps.executeQuery();
            while (rs.next()) {
                return rs.getString("assessor_code");
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            if (ps != null) {
                try {
                    ps.close();
                } catch (SQLException e) {
                    LOGGER.error(e.getMessage());
                }
            }
        }
        return null;
    }

    @Override
    public String getInspectionTypeDesc(Connection connection, Integer id) {
        PreparedStatement ps = null;
        ResultSet rs = null;
        String inspection = null;
        try {
            ps = connection.prepareStatement(SQL_GET_INSPECTION_TYPE_BY_REF_NO);
            ps.setInt(1, id);
            rs = ps.executeQuery();
            if (rs.next()) {
                inspection = rs.getString("inspection_type_desc");
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return inspection;
    }

    @Override
    public String getAssessorTypeByRefNo(Connection connection, String refNo) {
        PreparedStatement ps = null;
        try {
            ResultSet rs = null;
            ps = connection.prepareStatement(SELECT_ASSESSOR_TYPE_BY_REF_NO);
            ps.setString(1, refNo);
            rs = ps.executeQuery();
            while (rs.next()) {
                return rs.getString("assessor_type");
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return null;
    }

    @Override
    public boolean rejectJobStatusByJobId(Connection connection, int status, int reasonId, Integer refId) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(SQL_REJECT_CLAIM_ASSIGING_BY_PREVIOUS_JOB_ID);
            ps.setInt(1, status);
            ps.setInt(2, reasonId);
            ps.setInt(3, refId);
            return ps.executeUpdate() > 0;

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new Exception("System Error", e);
        } finally {
            ps.close();
        }
    }

    @Override
    public void updateJobFinishedDate(Connection connection, int refNo) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(SQL_UPDATE_JOB_FINISHED_DATE);
            ps.setString(1, Utility.sysDateTime());
            ps.setInt(2, refNo);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public List<UserDto> getReportingRteList(Connection connection) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<UserDto> reportingRteList = new ArrayList<>();
        try {
            String accessLevelRte = "21,22,23,24";
            ps = connection.prepareStatement(SQL_GET_REPORTING_RTE.replace("?", accessLevelRte));
            rs = ps.executeQuery();
            while (rs.next()) {
                UserDto user = new UserDto();
                user.setUserId(rs.getString("v_usrid"));
                user.setUserCode(rs.getInt("n_usrcode"));
                reportingRteList.add(user);
            }
            return reportingRteList;
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public boolean updateOnlineInspectionStatus(Connection connection, String statusId, int refNo) throws Exception {
        try (PreparedStatement ps = connection.prepareStatement(SQL_UPDATE_ONLINE_INSPECTION_STATUS_BY_REF_NO)) {
            ps.setString(1, statusId);
            ps.setInt(2, refNo);
            return ps.executeUpdate() > 0;
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
    }
}
